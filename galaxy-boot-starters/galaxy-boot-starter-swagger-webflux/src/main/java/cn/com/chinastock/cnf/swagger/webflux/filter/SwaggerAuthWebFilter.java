package cn.com.chinastock.cnf.swagger.webflux.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.swagger.webflux.properties.SwaggerProperties;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static java.util.Base64.getDecoder;

/**
 * SwaggerAuthWebFilter 类是WebFlux环境下的Swagger认证过滤器。
 * 该过滤器负责对Swagger UI相关路径进行Basic认证保护。
 * 
 * <p>过滤器的主要功能包括：</p>
 * <ul>
 *     <li>拦截Swagger UI相关路径的请求</li>
 *     <li>验证Basic认证头信息</li>
 *     <li>提供与WebMvc版本相同的认证功能</li>
 *     <li>支持响应式编程模型</li>
 * </ul>
 */
public class SwaggerAuthWebFilter implements WebFilter, Ordered {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BASIC_PREFIX = "Basic ";
    private static final String AUTHENTICATION_SCHEME = "Basic realm=\"Galaxy Swagger UI\"";
    
    private static final List<String> SWAGGER_PATHS = Arrays.asList(
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/v3/api-docs/**"
    );

    private final SwaggerProperties swaggerProperties;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public SwaggerAuthWebFilter(SwaggerProperties swaggerProperties) {
        this.swaggerProperties = swaggerProperties;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // 检查是否为Swagger相关路径
        if (!isSwaggerPath(path)) {
            return chain.filter(exchange);
        }

        String auth = request.getHeaders().getFirst(AUTHORIZATION_HEADER);

        if (!StringUtils.hasText(auth) || !auth.startsWith(BASIC_PREFIX)) {
            return sendUnauthorizedResponse(exchange.getResponse());
        }

        try {
            String base64Credentials = auth.substring(BASIC_PREFIX.length()).trim();
            String credentials = new String(getDecoder().decode(base64Credentials), StandardCharsets.UTF_8);
            String[] values = credentials.split(":", 2);

            if (values.length != 2 || !isValidCredentials(values[0], values[1])) {
                GalaxyLogger.warn(LogCategory.APP_LOG, "Invalid credentials attempt for path: {}", path);
                return sendUnauthorizedResponse(exchange.getResponse());
            }

            return chain.filter(exchange);
        } catch (IllegalArgumentException e) {
            GalaxyLogger.warn(LogCategory.APP_LOG, "Failed to decode basic authentication for path: {}", path, e);
            return sendUnauthorizedResponse(exchange.getResponse());
        }
    }

    private boolean isSwaggerPath(String path) {
        return SWAGGER_PATHS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    private boolean isValidCredentials(String username, String password) {
        return constantTimeEquals(username, swaggerProperties.getAuth().getUsername()) &&
                constantTimeEquals(password, swaggerProperties.getAuth().getPassword());
    }

    private Mono<Void> sendUnauthorizedResponse(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("WWW-Authenticate", AUTHENTICATION_SCHEME);
        return response.setComplete();
    }

    private boolean constantTimeEquals(String a, String b) {
        byte[] bytes1 = a.getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = b.getBytes(StandardCharsets.UTF_8);
        return java.security.MessageDigest.isEqual(bytes1, bytes2);
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
