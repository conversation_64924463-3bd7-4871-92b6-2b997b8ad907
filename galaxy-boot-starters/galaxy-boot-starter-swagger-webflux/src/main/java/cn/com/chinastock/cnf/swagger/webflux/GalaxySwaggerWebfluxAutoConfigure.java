package cn.com.chinastock.cnf.swagger.webflux;

import cn.com.chinastock.cnf.swagger.webflux.annotations.ApiMetaCodesCollector;
import cn.com.chinastock.cnf.swagger.webflux.annotations.ApiMetaCodesCustomizer;
import cn.com.chinastock.cnf.swagger.webflux.filter.SwaggerAuthWebFilter;
import cn.com.chinastock.cnf.swagger.webflux.properties.SwaggerProperties;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.properties.SpringDocConfigProperties;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 自动配置类，用于配置 Swagger WebFlux 相关的属性。
 * <p>
 * 由于 Spring Boot Security 的限制，这里采用的是WebFilter的方式，对 Swagger 的访问进行权限控制。
 * 该配置类专为WebFlux环境设计，提供与WebMvc版本相同的功能和配置方式。
 */
@Configuration
@EnableConfigurationProperties(SwaggerProperties.class)
public class GalaxySwaggerWebfluxAutoConfigure {
    private final SwaggerProperties swaggerProperties;

    public GalaxySwaggerWebfluxAutoConfigure(SwaggerProperties swaggerProperties) {
        this.swaggerProperties = swaggerProperties;
    }

    @Bean
    @Primary
    public SwaggerUiConfigProperties swaggerUiConfig() {
        SwaggerUiConfigProperties config = new SwaggerUiConfigProperties();
        config.setPath("/swagger-ui.html");
        config.setEnabled(true);
        return config;
    }

    @Bean
    @Primary
    public SpringDocConfigProperties springDocConfig() {
        SpringDocConfigProperties properties = new SpringDocConfigProperties();
        properties.setApiDocs(new SpringDocConfigProperties.ApiDocs());
        properties.getApiDocs().setPath("/v3/api-docs");
        return properties;
    }

    @Bean
    public SwaggerAuthWebFilter swaggerAuthWebFilter() {
        return new SwaggerAuthWebFilter(swaggerProperties);
    }

    @Bean
    public ApiMetaCodesCollector apiMetaCodesCollector() {
        return new ApiMetaCodesCollector();
    }

    @Bean
    public OpenApiCustomizer openApiCustomizer() {
        return new ApiMetaCodesCustomizer();
    }
}
