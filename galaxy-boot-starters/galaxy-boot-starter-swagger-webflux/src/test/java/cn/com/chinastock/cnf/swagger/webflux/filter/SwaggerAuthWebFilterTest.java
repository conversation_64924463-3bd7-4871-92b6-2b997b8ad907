package cn.com.chinastock.cnf.swagger.webflux.filter;

import cn.com.chinastock.cnf.swagger.webflux.properties.SwaggerProperties;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.util.Base64;

import static org.mockito.Mockito.*;

/**
 * SwaggerAuthWebFilter 的单元测试类
 */
class SwaggerAuthWebFilterTest {

    private SwaggerAuthWebFilter createFilter() {
        SwaggerProperties swaggerProperties = new SwaggerProperties();
        swaggerProperties.getAuth().setUsername("admin");
        swaggerProperties.getAuth().setPassword("secret");
        return new SwaggerAuthWebFilter(swaggerProperties);
    }

    private ServerWebExchange createMockExchange(String path, String authHeader) {
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerHttpResponse response = mock(ServerHttpResponse.class);
        HttpHeaders requestHeaders = new HttpHeaders();
        HttpHeaders responseHeaders = new HttpHeaders();

        if (authHeader != null) {
            requestHeaders.add("Authorization", authHeader);
        }

        when(exchange.getRequest()).thenReturn(request);
        when(exchange.getResponse()).thenReturn(response);
        when(request.getHeaders()).thenReturn(requestHeaders);
        when(request.getURI()).thenReturn(URI.create("http://localhost:8080" + path));
        when(response.setComplete()).thenReturn(Mono.empty());
        when(response.getHeaders()).thenReturn(responseHeaders);

        return exchange;
    }

    @Test
    void should_pass_through_non_swagger_paths() {
        SwaggerAuthWebFilter filter = createFilter();
        ServerWebExchange exchange = createMockExchange("/api/users", null);
        WebFilterChain chain = mock(WebFilterChain.class);
        when(chain.filter(exchange)).thenReturn(Mono.empty());

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void should_return_unauthorized_when_no_auth_header() {
        SwaggerAuthWebFilter filter = createFilter();
        ServerWebExchange exchange = createMockExchange("/swagger-ui.html", null);
        WebFilterChain chain = mock(WebFilterChain.class);

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        verify(exchange.getResponse()).setStatusCode(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void should_pass_through_with_valid_credentials() {
        SwaggerAuthWebFilter filter = createFilter();
        String validAuth = "Basic " + Base64.getEncoder().encodeToString("admin:secret".getBytes());
        ServerWebExchange exchange = createMockExchange("/swagger-ui.html", validAuth);
        WebFilterChain chain = mock(WebFilterChain.class);
        when(chain.filter(exchange)).thenReturn(Mono.empty());

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void should_return_unauthorized_when_invalid_credentials() {
        SwaggerAuthWebFilter filter = createFilter();
        String invalidAuth = "Basic " + Base64.getEncoder().encodeToString("wrong:credentials".getBytes());
        ServerWebExchange exchange = createMockExchange("/swagger-ui.html", invalidAuth);
        WebFilterChain chain = mock(WebFilterChain.class);

        StepVerifier.create(filter.filter(exchange, chain))
                .verifyComplete();

        verify(exchange.getResponse()).setStatusCode(HttpStatus.UNAUTHORIZED);
    }
}
