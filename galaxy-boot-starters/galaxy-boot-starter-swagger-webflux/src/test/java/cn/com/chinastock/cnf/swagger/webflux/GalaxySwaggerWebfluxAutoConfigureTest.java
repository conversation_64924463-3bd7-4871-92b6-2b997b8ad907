package cn.com.chinastock.cnf.swagger.webflux;

import cn.com.chinastock.cnf.swagger.webflux.annotations.ApiMetaCodesCollector;
import cn.com.chinastock.cnf.swagger.webflux.annotations.ApiMetaCodesCustomizer;
import cn.com.chinastock.cnf.swagger.webflux.filter.SwaggerAuthWebFilter;
import cn.com.chinastock.cnf.swagger.webflux.properties.SwaggerProperties;
import org.junit.jupiter.api.Test;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.properties.SpringDocConfigProperties;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * GalaxySwaggerWebfluxAutoConfigure 的单元测试类
 */
class GalaxySwaggerWebfluxAutoConfigureTest {
    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxySwaggerWebfluxAutoConfigure.class))
            .withPropertyValues(
                    "galaxy.swagger.auth.username=testuser",
                    "galaxy.swagger.auth.password=testpass"
            );

    @Test
    void should_create_swagger_ui_config() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SwaggerUiConfigProperties.class);
            SwaggerUiConfigProperties properties = context.getBean(SwaggerUiConfigProperties.class);
            assertThat(properties.getPath()).isEqualTo("/swagger-ui.html");
            assertThat(properties.isEnabled()).isTrue();
        });
    }

    @Test
    void should_create_spring_doc_config() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SpringDocConfigProperties.class);
            SpringDocConfigProperties properties = context.getBean(SpringDocConfigProperties.class);
            assertThat(properties.getApiDocs().getPath()).isEqualTo("/v3/api-docs");
        });
    }

    @Test
    void should_create_swagger_properties() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SwaggerProperties.class);
            SwaggerProperties properties = context.getBean(SwaggerProperties.class);
            assertThat(properties.getAuth().getUsername()).isEqualTo("testuser");
            assertThat(properties.getAuth().getPassword()).isEqualTo("testpass");
        });
    }

    @Test
    void should_create_swagger_auth_web_filter() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SwaggerAuthWebFilter.class);
        });
    }

    @Test
    void should_create_api_meta_codes_collector() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(ApiMetaCodesCollector.class);
        });
    }

    @Test
    void should_create_open_api_customizer() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(OpenApiCustomizer.class);
            assertThat(context.getBean(OpenApiCustomizer.class)).isInstanceOf(ApiMetaCodesCustomizer.class);
        });
    }

    @Test
    void should_use_default_credentials_when_not_configured() {
        new ApplicationContextRunner()
                .withConfiguration(AutoConfigurations.of(GalaxySwaggerWebfluxAutoConfigure.class))
                .run(context -> {
                    SwaggerProperties properties = context.getBean(SwaggerProperties.class);
                    assertThat(properties.getAuth().getUsername()).isEqualTo("user");
                    assertThat(properties.getAuth().getPassword()).isEqualTo("password");
                });
    }
}
